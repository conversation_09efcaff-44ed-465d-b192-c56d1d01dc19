/* Medium-like typography styles */

/* Article content styling */
.article-content {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #292929;
  line-height: 1.8;
  letter-spacing: -0.003em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* First paragraph styling with drop cap */
.article-content > div > p:first-of-type {
  font-size: 1.25rem;
  line-height: 1.7;
  margin-bottom: 2rem;
  color: #292929;
}

/* Heading styles */
.article-content h1,
.article-content h2,
.article-content h3 {
  font-family: 'Poppins', Georgia, 'Times New Roman', Times, serif;
  letter-spacing: -0.015em;
  color: #212121;
}

/* Paragraph spacing and styling */
.article-content p {
  margin-bottom: 1.75rem;
  font-size: 1.125rem;
}

/* Link styling */
.article-content a {
  color: #E50914;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.article-content a:hover {
  color: #B81D24;
  text-decoration-thickness: 2px;
}

/* Blockquote styling */
.article-content blockquote {
  border-left: 3px solid #E50914;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #555;
}

/* Code styling */
.article-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'JetBrains Mono', Menlo, Monaco, Consolas, 'Courier New', monospace;
  font-size: 0.9em;
}

/* List styling */
.article-content ul,
.article-content ol {
  margin-bottom: 1.75rem;
  padding-left: 1.5rem;
}

.article-content ul {
  list-style-type: disc;
}

.article-content ol {
  list-style-type: decimal;
}

.article-content li {
  margin-bottom: 0.75rem;
  padding-left: 0.5rem;
  line-height: 1.6;
}

.article-content li::marker {
  color: #555;
}

/* Nested list styling */
.article-content ul ul,
.article-content ol ol,
.article-content ul ol,
.article-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Image and figure styling */
.article-content figure {
  margin: 2.5rem auto;
}

.article-content figcaption {
  text-align: center;
  font-size: 0.875rem;
  color: #6b6b6b;
  margin-top: 0.75rem;
  font-style: italic;
}

/* Horizontal rule styling */
.article-content hr {
  margin: 3rem auto;
  width: 30%;
  border: 0;
  border-top: 1px solid #e5e5e5;
}

/* Table styling */
.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.9rem;
}

.article-content th {
  background-color: #f5f5f5;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
  border-bottom: 2px solid #e5e5e5;
}

.article-content td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e5e5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .article-content {
    width: 100%;
    padding: 0;
    line-height: 1.7; /* Slightly reduced line height for better mobile reading */
  }

  .article-content p {
    font-size: 1.05rem; /* Slightly larger for better readability */
    width: 100%;
    margin-bottom: 1.5rem; /* Reduced bottom margin */
    letter-spacing: -0.01em; /* Slightly tighter letter spacing */
  }

  .article-content > div > p:first-of-type {
    font-size: 1.15rem;
    line-height: 1.6;
    margin-bottom: 1.75rem;
  }

  .article-content blockquote {
    margin: 1.5rem 0;
    padding-left: 1rem;
    font-size: 1rem;
  }

  .article-content h1 {
    font-size: 1.75rem;
    line-height: 1.3;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .article-content h2 {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-top: 1.75rem;
    margin-bottom: 0.75rem;
  }

  .article-content h3 {
    font-size: 1.25rem;
    line-height: 1.3;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .article-content table {
    font-size: 0.85rem;
    display: block;
    overflow-x: auto;
    margin: 1.5rem 0;
  }

  .article-content figure {
    margin: 1.5rem auto;
    width: 100% !important;
  }

  .article-content img {
    width: 100%;
    height: auto;
    border-radius: 4px; /* Slight rounding of images */
  }

  /* Improve list readability on mobile */
  .article-content ul,
  .article-content ol {
    padding-left: 1.25rem;
    margin-bottom: 1.5rem;
    margin-top: 1rem;
  }

  .article-content ul {
    list-style-type: disc;
  }

  .article-content ol {
    list-style-type: decimal;
  }

  .article-content li {
    margin-bottom: 0.75rem;
    padding-left: 0.25rem;
    line-height: 1.6;
    font-size: 1.05rem;
  }

  .article-content li::marker {
    color: #666;
  }

  /* Nested lists on mobile */
  .article-content ul ul,
  .article-content ol ol,
  .article-content ul ol,
  .article-content ol ul {
    margin-top: 0.5rem;
    margin-bottom: 0.25rem;
    padding-left: 1rem;
  }

  .article-content li > ul,
  .article-content li > ol {
    margin-bottom: 0;
  }

  /* Improve code readability */
  .article-content pre {
    padding: 1rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    border-radius: 4px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .article-content {
    line-height: 1.65; /* Further optimize line height for very small screens */
    font-feature-settings: "kern" 1, "liga" 1; /* Enable kerning and ligatures for better readability */
  }

  /* Special class for mobile-optimized text */
  .mobile-optimized-text {
    font-size: 1rem;
    line-height: 1.6;
    letter-spacing: -0.01em;
    word-spacing: 0.01em;
  }

  .article-content p {
    font-size: 1rem; /* Slightly larger than before for better readability */
    margin-bottom: 1.25rem; /* Further reduced spacing */
  }

  .article-content > div > p:first-of-type {
    font-size: 1.1rem;
    line-height: 1.6;
  }

  /* Optimize drop cap for small screens */
  .article-content .first-letter\:text-4xl {
    font-size: 3.5rem !important; /* Slightly smaller drop cap */
  }

  .article-content h1 {
    font-size: 1.5rem;
    margin-top: 1.75rem;
    margin-bottom: 0.75rem;
  }

  .article-content h2 {
    font-size: 1.3rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .article-content h3 {
    font-size: 1.15rem;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .article-content blockquote {
    font-size: 0.95rem;
    padding-left: 0.75rem;
    margin: 1.25rem 0;
    border-left: 2px solid #E50914; /* Slightly thinner border */
  }

  .article-content code {
    font-size: 0.85em;
    padding: 0.15em 0.3em; /* Slightly reduced padding */
  }

  /* Improve spacing for lists on very small screens */
  .article-content ul,
  .article-content ol {
    padding-left: 1rem;
    margin-bottom: 1.25rem;
    margin-top: 0.75rem;
  }

  .article-content li {
    margin-bottom: 0.6rem;
    padding-left: 0.15rem;
    line-height: 1.5;
    font-size: 1rem;
  }

  /* Improve nested list spacing on very small screens */
  .article-content ul ul,
  .article-content ol ol,
  .article-content ul ol,
  .article-content ol ul {
    padding-left: 0.85rem;
    margin-top: 0.4rem;
    margin-bottom: 0.2rem;
  }

  /* Adjust list markers for better visibility on small screens */
  .article-content li::marker {
    font-size: 0.9em;
  }

  /* Optimize table display on very small screens */
  .article-content table {
    font-size: 0.8rem;
  }

  .article-content th,
  .article-content td {
    padding: 0.5rem;
  }
}
