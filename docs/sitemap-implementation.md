# Sitemap Implementation Guide

This document provides a comprehensive overview of the sitemap implementation for the urTechy Blog.

## Overview

The blog uses two types of sitemaps:
1. **Main Sitemap**: Generated by next-sitemap at build time
2. **News Sitemap**: Dynamically generated by an API route

## Implementation Details

### News Sitemap

The news sitemap is implemented as a dynamic API route that:
- Fetches the latest posts from Hygraph
- Generates XML in the Google News sitemap format
- Is accessible at both `/sitemap-news.xml` and `/api/sitemap-news`

#### Key Files

1. **API Route**: `pages/api/sitemap-news.js`
   - Dynamically generates the sitemap XML
   - Sets appropriate cache headers
   - Handles errors gracefully

2. **URL Rewrite**: In `next.config.js`
   ```javascript
   {
     source: "/sitemap-news.xml",
     destination: "/api/sitemap-news",
   }
   ```
   - Maintains backward compatibility
   - Allows search engines to access the sitemap at the original URL

3. **Robots.txt Configuration**: In `next-sitemap.config.js`
   ```javascript
   additionalSitemaps: [
     "https://blog.urtechy.com/sitemap.xml",
     "https://blog.urtechy.com/sitemap-news.xml",
   ]
   ```
   - References the sitemap in robots.txt

### Webhook Integration

The sitemap is automatically updated when content changes in Hygraph via a webhook:

1. **Webhook Endpoint**: `pages/api/revalidate-sitemap.js`
   - Validates the webhook secret
   - Checks for relevant content types and operations
   - Revalidates dynamic pages

2. **Hygraph Configuration**:
   - Webhook URL: `https://blog.urtechy.com/api/revalidate-sitemap?secret=YOUR_SECRET`
   - Payload format includes `__typename` to identify content type

## Why This Approach?

This implementation solves several challenges:

1. **Vercel's Read-Only Filesystem**: Serverless functions can't write to the filesystem
2. **Next.js Route Conflicts**: Avoids conflicts between static files and dynamic routes
3. **SEO Compatibility**: Maintains backward compatibility for search engines
4. **Performance**: Uses cache headers to optimize performance

## Testing

To test the implementation:

1. **Access the sitemap**:
   - Visit `https://blog.urtechy.com/sitemap-news.xml`
   - Verify it contains the latest posts

2. **Test the webhook**:
   ```bash
   curl -X POST "https://blog.urtechy.com/api/revalidate-sitemap?secret=YOUR_SECRET" \
     -H "Content-Type: application/json" \
     -d '{"operation":"publish","data":{"id":"test-id","slug":"test-slug","__typename":"Post"}}'
   ```

## Maintenance

When adding new content types or changing the sitemap format:

1. Update the `getNewsArticles` function in `services/sitemap-utils.js`
2. Adjust the XML generation in `pages/api/sitemap-news.js`
3. Test both the direct API route and the rewritten URL

## Troubleshooting

Common issues and solutions:

1. **404 Error**: Check that the URL rewrite is correctly configured in `next.config.js`
2. **Webhook Failures**: Verify the payload format matches what the API expects
3. **Missing Content**: Ensure the GraphQL query in `getNewsArticles` is fetching all required posts

## Future Improvements

Potential enhancements:

1. Add monitoring for sitemap generation errors
2. Implement more granular caching strategies
3. Add support for additional sitemap types (image, video)
