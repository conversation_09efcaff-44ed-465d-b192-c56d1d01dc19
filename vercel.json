{"version": 2, "buildCommand": "next build", "installCommand": "npm install", "framework": "nextjs", "regions": ["bom1"], "headers": [{"source": "/(.*)\\.(jpg|jpeg|gif|mp4|png|webp|css|js|ico|svg|woff|woff2)$", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "/(.*)\\.(html)$", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600, stale-while-revalidate=86400"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/firebase-proxy", "headers": [{"key": "Cache-Control", "value": "no-store, private"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}, {"source": "/api/((?!firebase-proxy).*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=60, s-maxage=60, stale-while-revalidate=300"}]}, {"source": "/_next/image(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}], "redirects": [{"source": "/feed", "destination": "/api/rss", "permanent": true}, {"source": "/rss", "destination": "/api/rss", "permanent": true}], "build": {"env": {"NEXT_PUBLIC_HYGRAPH_CDN_API": "https://ap-south-1.cdn.hygraph.com/content/cky5wgpym15ym01z44tk90zeb/master", "NEXT_PUBLIC_HYGRAPH_CONTENT_API": "https://api-ap-south-1.hygraph.com/v2/cky5wgpym15ym01z44tk90zeb/master"}}}