# Production Environment Variables Template
# Copy this file to .env.production.local for production builds
# NEVER commit actual production secrets to version control

# Firebase Configuration (Public - safe to expose)
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# Google Maps API (Restrict this key to your production domain)
NEXT_PUBLIC_GOOGLE_MAP_API=your-google-maps-api-key

# Hygraph API Configuration (Public endpoints - safe to expose)
NEXT_PUBLIC_HYGRAPH_CONTENT_API=https://api-ap-south-1.hygraph.com/v2/your-project-id/master
NEXT_PUBLIC_HYGRAPH_CDN_API=https://ap-south-1.cdn.hygraph.com/content/your-project-id/master
NEXT_PUBLIC_HYGRAPH_MANAGEMENT_API=https://management-ap-south-1.hygraph.com/graphql

# SENSITIVE: Server-side only secrets (Set these in your hosting platform's environment variables)
HYGRAPH_AUTH_TOKEN=your-hygraph-auth-token
HYGRAPH_WEBHOOK_SECRET=your-webhook-secret

# For backward compatibility (can be removed later)
NEXT_PUBLIC_GRAPHCMS_ENDPOINT=${NEXT_PUBLIC_HYGRAPH_CONTENT_API}
GRAPHCMS_TOKEN=${HYGRAPH_AUTH_TOKEN}

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS=your-ga-tracking-id
NEXT_PUBLIC_ENABLE_ANALYTICS_IN_DEV=false

# Production optimizations
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
